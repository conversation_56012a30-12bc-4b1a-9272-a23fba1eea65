import { Plus, Trash, X } from "@tamagui/lucide-icons";

import { Button, XStack, YStack } from "tamagui";
import { Image } from "react-native";

type AddDocumentsProps = {
  onAddDocument: () => void;
  scannedImages?: string[];
  onRemoveImage?: (index: number) => void;
};

export default function AddDocuments({
  onAddDocument,
  scannedImages = [],
  onRemoveImage,
}: AddDocumentsProps) {
  return (
    <YStack {...styles.container}>
      <XStack
        {...styles.subContainer}
        justify={"center"}
        style={{ alignItems: "center" }}
      >
        <ImageContainer images={scannedImages} onRemoveImage={onRemoveImage} />
        <Button
          icon={<Plus size={"$3"} color="$confirmOrderTextColor" />}
          {...styles.addBtn}
          onPress={onAddDocument}
        />
      </XStack>
    </YStack>
  );
}

const ImageContainer = ({
  images = [],
  onRemoveImage,
}: {
  images?: string[];
  onRemoveImage?: (index: number) => void;
}) => {
  if (!images.length) return null;
  return (
    <XStack>
      {images.map((uri, idx) => (
        <YStack key={idx} {...styles.deleteImageContainer}>
          <Image
            source={{ uri }}
            style={{ width: "100%", height: "100%", borderRadius: 10 }}
            resizeMode="cover"
          />
          {onRemoveImage && (
            <Button
              {...styles.deleteIcon}
              onPress={() => onRemoveImage(idx)}
              aria-label="Remove image"
              chromeless
            >
              <Trash size={18} color="red" />
            </Button>
          )}
        </YStack>
      ))}
    </XStack>
  );
};

const styles = {
  container: {
    borderWidth: 1,
    borderRadius: 7,
    borderColor: "$primaryBorderColor" as any,
  },
  addBtn: {
    backgroundColor: "$confirmOrderBlue" as any,
    borderColor: "$confirmOrderBorderCOlor" as any,
    borderWidth: 1,
    borderRadius: 7,
    padding: 1,
  },
  subContainer: {
    marginBlock: 20,
    marginInline: 20,
  },
  deleteImageContainer: {
    width: 64,
    height: 64,
    borderRadius: 10,
    overflow: "hidden" as any,
    marginRight: 12,
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    alignItems: "center" as any,
    justifyContent: "center" as any,
    position: "relative" as any,
  },
  deleteIcon: {
    position: "absolute" as any,
    top: -1,
    right: -1,
    backgroundColor: "transparent",
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
    padding: 0,
    zIndex: 2,
    minWidth: 0,
    minHeight: 0,
    elevation: 20,
  },
};
