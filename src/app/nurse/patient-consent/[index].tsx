import { useLocalSearchParams, useRouter } from "expo-router";
import { useState, useEffect, useRef } from "react";
import { PatientInfo } from "../requestVisit";
import { But<PERSON>, Text, TextArea, View, YStack } from "tamagui";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { useStyles } from "./styles/style";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AddDocuments from "src/components/AddDocuments";
import ScanDocumentComponent from "src/components/ScanDocument";
import { SelectDocument } from "src/components/SelectDocuments";

export default function PatientConsentScreen() {
  const { index, selectedPatients, patientData } = useLocalSearchParams();
  const router = useRouter();

  const currentIndex = parseInt(index as string);
  const patients: PatientInfo[] = JSON.parse(selectedPatients as string);
  const patient = patients[currentIndex];

  const [localPatientData, setLocalPatientData] = useState(() => {
    try {
      return patientData ? JSON.parse(patientData as string) : {};
    } catch (e) {
      console.error("Failed to parse patientData:", e);
      return {};
    }
  });
  const [chiefComplaint, setChiefComplaint] = useState(
    localPatientData[patient.id]?.chiefComplaint || ""
  );
  const [scannedImages, setScannedImages] = useState<string[]>(
    localPatientData[patient.id]?.attachments || []
  );
  const [openSettingsDrawer, setOpenSettingsDrawer] = useState(false);
  const [showScanDocument, setShowScanDocument] = useState(false);
  const [showUploader, setShowUploader] = useState(false);
  const styles = useStyles();

  const scrollViewRef = useRef<any>(null);

  useEffect(() => {
    const patientDataForCurrent = localPatientData[patient.id] || {};
    setChiefComplaint(patientDataForCurrent.chiefComplaint || "");
    setScannedImages(patientDataForCurrent.attachments || []);
  }, [patient.id, currentIndex]);

  useEffect(() => {
    setLocalPatientData((prev:any) => {
      const newPatientData = {
        ...prev,
        [patient.id]: {
          chiefComplaint,
          attachments: scannedImages,
        },
      };
      if (
        JSON.stringify(prev[patient.id]) !==
        JSON.stringify(newPatientData[patient.id])
      ) {
        return newPatientData;
      }
      return prev;
    });
  }, [chiefComplaint, scannedImages, patient.id]);

  const handleBack = () => {
    if (currentIndex > 0) {
      router.push({
        pathname: "/nurse/patient-consent/[index]",
        params: {
          index: (currentIndex - 1).toString(),
          patientData: JSON.stringify(localPatientData),
          selectedPatients,
        },
      });
    } else {
      router.back();
    }
  };

  const handleNext = () => {
    if (currentIndex < patients.length - 1) {
      router.push({
        pathname: "/nurse/patient-consent/[index]",
        params: {
          index: (currentIndex + 1).toString(),
          patientData: JSON.stringify(localPatientData),
          selectedPatients,
        },
      });
    } else {
      router.push({
        pathname: "/nurse/telehealthconsent", 
        params: {
          patientData: JSON.stringify(localPatientData),
          selectedPatients,
        },
      });
    }
  };

  const handleAddDocument = () => {
    if (scannedImages.length >= 3) {
      return;
    }
    setShowUploader(true);
  };

  const handleRemoveImage = (url: string) => {
    setScannedImages((prev) => prev.filter((u) => u !== url));
  };

  const handleCloseScanDocument = (images: string[]) => {
    setShowScanDocument(false);
    setScannedImages(images);
  };

  const handleOpenScanDocument = () => {
    setShowUploader(false);
    setShowScanDocument(true);
  };

  const handleAddGalleryImage = (uri: string) => {
    setScannedImages((prev) => [...prev, uri]);
  };

  return (
    <View {...styles.container}>
      <KeyboardAwareScrollView
        innerRef={(ref) => (scrollViewRef.current = ref)}
        contentContainerStyle={{ flexGrow: 1 }}
        enableOnAndroid
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View {...styles.mainStack}>
          <ScreenHeader
            onAvatarPress={() => setOpenSettingsDrawer(true)}
            screenName="Back"
            onBackPress={handleBack}
          />

          <Text {...styles.patinetCountText}>
            PATIENT {currentIndex + 1} OF {patients?.length}
          </Text>
          <Text {...styles.telehealthConsentText}>
            Telehealth Informed Consent
          </Text>

          <View {...styles.patientCard}>
            <YStack gap={"$2"}>
              <Text {...styles.patientCardTitle}>{patient?.name}</Text>
              <Text {...styles.patientCardSubTitle}>DOB: {patient?.dob}</Text>
            </YStack>
          </View>

          <Text {...styles.complaintText}>Chief complaint</Text>
          <TextArea
            {...styles.complaintTextArea}
            placeholder="Please enter the details."
            placeholderTextColor={"$textcolor"}
            overflow="hidden"
            value={chiefComplaint}
            onChangeText={setChiefComplaint}
          />
          <YStack>
            <Text {...styles.attachmentsText}>Attachments</Text>
            <AddDocuments
              onAddDocument={handleAddDocument}
              scannedImages={scannedImages}
              onRemoveImage={handleRemoveImage}
              patientName={patient?.name}
            />
          </YStack>
        </View>
      </KeyboardAwareScrollView>

      <View {...styles.nextBtnContainer}>
        <Button
          {...(chiefComplaint.trim() === ""
            ? styles.nextBtnDisabled
            : styles.nextBtn)}
          disabled={chiefComplaint.trim() === ""}
          onPress={handleNext}
        >
          Agree & Next
        </Button>
      </View>

      {showScanDocument && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            backgroundColor: "#fff",
          }}
        >
          <ScanDocumentComponent
            open={showScanDocument}
            initialImages={scannedImages}
            onClose={handleCloseScanDocument}
            maxImages={3}
          />
        </View>
      )}
      {!showScanDocument && showUploader && (
        <SelectDocument
          open={showUploader}
          onClose={setShowUploader}
          onOpenScanDocument={handleOpenScanDocument}
          onAddImage={handleAddGalleryImage}
          scannedImages={scannedImages}
        />
      )}

      {openSettingsDrawer && (
        <SheetDemo open={openSettingsDrawer} setOpen={setOpenSettingsDrawer} />
      )}
    </View>
  );
}