import { useLocalSearchParams, useRouter } from "expo-router";
import { useState, useEffect } from "react";
import { PatientInfo } from "../requestVisit";
import { Button, Text, TextArea, View, YStack } from "tamagui";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { useStyles } from "./styles/style";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

export default function PatientConsentScreen() {
  const { index, selectedPatients, patientData } = useLocalSearchParams();
  const router = useRouter();

  const currentIndex = parseInt(index as string);
  const patients: PatientInfo[] = JSON.parse(selectedPatients as string);
  const patient = patients[currentIndex];
  const [chiefComplaint, setChiefComplaint] = useState("");
  const [openSettingsDrawer, setOpenSettingsDrawer] = useState(false);
  const styles = useStyles();

  const handleBack = () => {
    if (currentIndex > 0) {
      router.push({
        pathname: "/nurse/patient-consent/[index]",
        params: {
          index: (currentIndex - 1).toString(),
          selectedPatients,
        },
      });
    } else {
      router.back();
    }
  };

  return (
    <View {...styles.container}>
      <KeyboardAwareScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={50}
        extraHeight={50}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        resetScrollToCoords={{ x: 0, y: 0 }}
        scrollEnabled={true}
      >
        <View {...styles.mainStack}>
          <ScreenHeader
            onAvatarPress={() => setOpenSettingsDrawer(true)}
            screenName="Back"
            onBackPress={handleBack}
          />

          <Text {...styles.patinetCountText}>
            PATIENT {currentIndex + 1} OF {patients?.length}{" "}
          </Text>
          <Text {...styles.telehealthConsentText}>
            Telehealth Informed Consent
          </Text>

          <View {...styles.patientCard}>
            <YStack gap={"$2"}>
              <Text {...styles.patientCardTitle}>{patient?.name}</Text>
              <Text {...styles.patientCardSubTitle}>DOB: {patient?.dob}</Text>
            </YStack>
          </View>

          <Text {...styles.complaintText}>Chief complaint</Text>
          <TextArea
            {...styles.complaintTextArea}
            placeholder="Please enter the details."
            placeholderTextColor={"$textcolor"}
            overflow="hidden"
            value={chiefComplaint}
            onChangeText={setChiefComplaint}
          />
        </View>
      </KeyboardAwareScrollView>
      {openSettingsDrawer && (
        <SheetDemo open={openSettingsDrawer} setOpen={setOpenSettingsDrawer} />
      )}
    </View>

  );
}
