export const useStyles = () => {
  return {
    container: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
    },
    mainStack: {
      marginBlock: 20,
      marginInline: 20,
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
    patinetCountText:{
        marginBlockStart:30,
        marginBlockEnd:20,
        color: "$textcolor" as any,
        fontSize: 14,
        fontWeight: 600 as any,
    },
    telehealthConsentText:{
        marginBlockEnd:20,
        color: "$textcolor" as any,
        fontSize: 20,
        fontWeight: 600 as any,
    },
       patientCard: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 12,
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: 15,
      marginBlockEnd: 15,
    },
    patientCardTitle: {
      fontSize: 16,
      fontWeight: 600 as any,
      color: "$textcolor" as any,
    },
    patientCardSubTitle: {
      fontSize: 14,
      fontWeight: 400 as any,
      color: "$textcolor" as any,
    },
    complaintText:{
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart:200,
      marginBlockEnd:10,
    },
    complaintTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$1" as "$1",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 8,
      textAlignVertical: "top" as any,
    },

  };
};
